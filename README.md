## gitops-cluster-bootstrap

This is an app-of-apps style startup repository for ArgoCD that sets up a production-like environment with essential workloads including observability, security, and example applications.

## Architecture

This repository follows the GitOps pattern using ArgoCD's app-of-apps deployment style. The configuration is centralized in the `bootstrap` root app with its settings in `values.yaml`, which propagates to child applications.

### Project Structure

- **bootstrap**: Contains application templates for both Helm charts and Git repositories
- **bootstrap-resources**: Contains application-specific resources (network policies, ingress settings, service accounts) that are continuously synced with the cluster
- **templates**: Houses individual application configurations

<img src="images/4.png" width="1000px;">

## Included Applications

| Application            | Source                                                  | Description |
|------------------------|--------------------------------------------------------|-------------|
| Cats                   | https://github.com/AlexsJones/cats                      | Example application |
| Cert Manager           | https://charts.jetstack.io                              | Certificate management |
| Cloudflared           | https://cloudflare.github.io/helm-charts               | Cloudflare tunnel integration |
| K8sgpt                 | https://charts.k8sgpt.ai/                               | Kubernetes debugging tool |
| Local Path Provisioner | https://github.com/rancher/local-path-provisioner.git   | Local storage provisioner |
| Logging                | https://grafana.github.io/helm-charts                   | Logging stack |
| MetalLB                | https://metallb.github.io/metallb                       | Load balancer |
| N8N                    | 8gears.container-registry.com/library                   | Workflow automation |
| Nginx Ingress         | https://helm.nginx.com/stable                           | Ingress controller |
| Observability         | https://prometheus-community.github.io/helm-charts      | Monitoring stack |
| Trivy                  | https://aquasecurity.github.io/helm-charts/             | Security scanning |

## Configuration

The `values.yaml` file contains global and application-specific configurations:

### Global Settings
- **domain**: Set your domain for nginx ingress and services
- **storage.local.enable**: Enable local path provisioner

### Application Settings
- **cats.storageClass**: Storage class for Cats application
- **observability**:
  - storageClass: Storage class for observability stack
  - storageSize: Storage size (default: 10Gi)
  - retention: Data retention period (default: 5d)
- **metallb.addresspool**: IP range for MetalLB
- **cloudflare**: Tunnel configuration settings

## Prerequisites

1. A running Kubernetes cluster
2. The following CLI tools:
   - kubectl
   - kubectx
   - k9s
   - jq
   - kustomize
   - helm
   - sshuttle

## Installation

1. Install ArgoCD using Helm:
```bash
helm repo add argo https://argoproj.github.io/argo-helm
helm repo update

helm upgrade --install argocd argo/argo-cd \
  --namespace argocd \
  --create-namespace \
  -f argocd/values.yaml \
  --wait
```

2. Get the ArgoCD admin password:
```bash
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d
```

3. Deploy the bootstrap application:
```bash
kubectl apply -f bootstrap.yaml
```

4. Configure your settings by editing `values.yaml`

## License