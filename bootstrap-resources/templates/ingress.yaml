{{- if and .Values.domain .Values.ingress }}
{{- range $name, $ingress := .Values.ingress }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-{{ $name }}
  namespace: {{ $ingress.namespace }}
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/issuer: "letsencrypt-ci"
    {{- with $ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  tls:
  - hosts:
    - {{ $ingress.host | default (printf "%s.%s" $name $.Values.domain) | quote }}
    secretName: nginx-{{ $name }}-tls
  rules:
  - host: {{ $ingress.host | default (printf "%s.%s" $name $.Values.domain) | quote }}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {{ $ingress.service.name }}
            port:
              number: {{ $ingress.service.port | default 80 }}
---
{{- end }}
{{- end }}