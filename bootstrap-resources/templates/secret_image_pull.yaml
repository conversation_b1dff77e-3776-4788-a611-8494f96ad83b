{{- if and .Values.imagePullSecret.registry .Values.imagePullSecret.username .Values.imagePullSecret.password .Values.imagePullSecret.email }}
{{- $registry := .Values.imagePullSecret.registry }}
{{- $username := .Values.imagePullSecret.username }}
{{- $password := .Values.imagePullSecret.password }}
{{- $email := .Values.imagePullSecret.email }}
{{- $auth := printf "%s:%s" $username $password | b64enc }}
{{- $dockerconfigjson := dict "auths" (dict $registry (dict "username" $username "password" $password "email" $email "auth" $auth)) | toJson | b64enc }}
{{- range .Values.imagePullSecret.namespace }}
apiVersion: v1
kind: Secret
metadata:
  name: image-pull-secret
  namespace: {{ . }}
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: {{ $dockerconfigjson | quote }}
---
{{- end }}
{{- end }}