name: Update Container Image

# This workflow is triggered by the backend build in the redhat-developer-demos/rps-game-manifests repo.
# To manually trigger the action, you can use curl with a PAT:
# curl -X POST \
# -H "Authorization: Bearer $PAT" \
# -H "Accept: application/vnd.github.v3+json" \
# https://api.github.com/repos/evanshortiss/gitops-gh-actions-manifests/actions/workflows/update-container-image.yaml/dispatches \
# -d '{"ref":"main", "inputs": { "image": "quay.io/orgoruser/image:tag", "message": "some changes" } }'
on:
  workflow_dispatch:
    inputs:
      message:
        required: true
        description: A message describing why this workflow is being invoked
      image:
        required: true
        description: The image that triggered this workflow
      sha:
        required: true
        description: The short hash of the commit the invoked this workflow
      repository:
        required: true
        description: The repository that triggered this workflow
      tag:
        required: true
        description: The tag of the image that triggered this workflow

jobs:
  update-image-tag:
    runs-on: ubuntu-latest

    permissions:
      contents: write
      pull-requests: write

    steps:
    # Checkout the source code in the repository
    - uses: actions/checkout@v4

    - name: Print new image value
      run: echo "New image is ${{github.event.inputs.image}}"

    - name: Install yq
      run: |
        sudo curl -L https://github.com/mikefarah/yq/releases/download/v4.35.2/yq_linux_amd64 -o /usr/local/bin/yq
        sudo chmod +x /usr/local/bin/yq

    # Update both values files (production and non-production resources)
    - name: Update values.yaml
      run: |
        REPO_NAME=$(echo "${{ github.event.inputs.repository }}" | cut -d'/' -f2)
        yq -i e ".apps.$REPO_NAME.image.tag = \"${{ github.event.inputs.tag }}\"" values.yaml
        yq -i e ".apps.$REPO_NAME.image.repository = \"${{ github.event.inputs.image }}\"" values.yaml

    # Create a PR with the new values files. This requires actions to
    # be allowed to open a PR in the repository:
    # https://github.com/marketplace/actions/create-pull-request#workflow-permissions
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v7.0.8
      id: cpr
      with:
        token: ${{ secrets.PAT }}
        branch: update-image-${{github.event.inputs.sha}}
        delete-branch: true
        title: '[GitOps][${{github.event.inputs.repository}}] ${{github.event.inputs.message}}'
        commit-message: update ${{github.event.inputs.repository}} image tag to ${{github.event.inputs.tag}}
        assignees: ${{ github.actor }}

    - name: Check outputs
      if: ${{ steps.cpr.outputs.pull-request-number }}
      run: |
        echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
        echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"
