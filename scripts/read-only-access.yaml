apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: read-only
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: [""]
- apiGroups:
  - ""
  resources:
  - 'pods'
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - extensions
  resources:
  - '*'
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - '*'
  verbs:
  - get
  - list
  - watch
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: read-only-binding
subjects:
- kind: ServiceAccount
  name: read-only
  namespace: default
roleRef:
  kind: ClusterRole
  name: read-only
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: Secret
metadata:
  name: sa1-token
  namespace: default
  annotations:
    kubernetes.io/service-account.name: read-only
type: kubernetes.io/service-account-token
---

apiVersion: v1
kind: ServiceAccount
metadata:
  name: read-only
  namespace: default
secrets:
  - name: sa1-token
