# Global
## Set the domain to create nginx for argocd and other service ingress
domain: heaplog.com
## Set local if you wish to start the local path provisioner for "local-path" in your cluster
storage:
  local:
    enable: true
# Application specific
cats:
  storageClass: "local-path"
# Grafana configuration
observability:
  storageClass: "local-path"
  pdkey:
  storageSize: 10Gi
  retention: 5d
# Trivy configuration
trivy:
  enable: true
# Logging configuration
logging:
  enable: true
# Metallb configuration
metallb:
  # This only passes through to the bootstrap resources a single address pool,
  # you can use a range if you you want to do something more complex
  addresspool: *************-*************
# cloudflare configuration
cloudflare:
  # Your Cloudflare account number.
  account: "78a5b1d27df7ba9ee2a533f734dcc0d1"
  # The name of your tunnel
  tunnelName: "k8s-argocd"
  # The ID of your tunnel
  tunnelId: "0d650b4c-bc5c-4902-bb75-4aacf7d8cc62"
  # The secret for tunnel authentication
  secret: "7fX3Zlzh9DzotzOCMAzUHqQC68+GS5V1vIZGHvCD8K8="
  # Ingress configurations
  services:
  - name: n8n
    prefix: n8n
    namespace: n8n
    port: 80
    protocol: http
  - name: nginx-ingress-nginx-controller
    namespace: ingress-nginx
    port: 443
    protocol: https
    isDefault: true
    originRequest:
      noTLSVerify: true
# Tailscale configuration
tailscale:
  # Client ID and secret for the Tailscale client
  clientId: "kXMBD6a1h311CNTRL"
  clientSecret: "tskey-client-kJKviiEYeD11CNTRL-SV9i7ZKyiEcAGnhikB4XEcA1Xp61X9WrQ"
# Image pull secret for pulling images from private registries
imagePullSecret:
  registry: https://ghcr.io
  username: dungxtd
  password: ****************************************
  email: <EMAIL>
  namespace:
  - portfolio
# Ingress configuration for Nginx
ingress:
  argocd:
    namespace: argocd
    service:
      name: argocd-server
      port: 80
    annotations:
      nginx.ingress.kubernetes.io/proxy-buffer-size: "8k"
      # nginx.ingress.kubernetes.io/server-snippet: |
      #   client_header_buffer_size 32k;
      #   large_client_header_buffers 4 32k;
      nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
      nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/websocket-services: "argocd-server"
  grafana:
    namespace: observability
    service:
      name: observability-grafana
      port: 80
  cat:
    namespace: cats
    service:
      name: cats
  jackett:
    namespace: media
    service:
      name: jackett
      port: 9117
  portfolio:
    host: me.heaplog.com
    namespace: portfolio
    service:
      name: portfolio
  
  rancher:
    host: rancher.heaplog.com
    namespace: cattle-system
    service:
      name: rancher
      port: 80
# Application configuration
apps:
  portfolio:
    image:
      repository: ghcr.io/dungxtd/portfolio
      tag: sha-e2dc31f
    containerPort: 3000
  cats:
    image:
      repository: ghcr.io/alexsjones/cats
      tag: main
    containerPort: 8080
    storage: false
    storageClassName: "local-path"
    storageSize: 1Gi
