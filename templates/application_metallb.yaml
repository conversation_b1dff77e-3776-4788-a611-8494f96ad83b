apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: metallb
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: 'https://metallb.github.io/metallb'
    chart: metallb
    targetRevision: 0.15.2
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: metallb-system
  syncPolicy:
    automated: {}
    syncOptions:
      - CreateNamespace=true
