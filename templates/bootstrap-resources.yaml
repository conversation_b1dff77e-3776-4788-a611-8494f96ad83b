apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: bootstrap-resources
  namespace: argocd
spec:
  destination:
    namespace: argocd
    server: https://kubernetes.default.svc
  project: default
  source:
    path: bootstrap-resources
    repoURL: **************:dungxtd/gitops-kubernetes-bootstrap.git
    targetRevision: HEAD
    helm:
      values: |
        domain: {{.Values.domain }}
        metallb:
          addresspool: {{.Values.metallb.addresspool }}
        {{- with .Values.imagePullSecret }}
        imagePullSecret:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.ingress }}
        ingress:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        clusterRoleBinding:
          name: k8s-admins-full-access
          clusterRole: cluster-admin
          group: system:masters
  syncPolicy:
    automated:
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
