apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: nginx
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  destination:
    namespace: ingress-nginx
    server: https://kubernetes.default.svc
  project: default
  source:
    chart: ingress-nginx
    helm:
      parameters:
      - name: controller.kind
        value: DaemonSet
      - name: controller.service.type
        value: ClusterIP
    repoURL: https://kubernetes.github.io/ingress-nginx
    targetRevision: 4.12.3
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true
