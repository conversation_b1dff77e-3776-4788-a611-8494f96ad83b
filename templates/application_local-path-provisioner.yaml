{{- if .Values.storage.local.enable }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: localpath
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-1"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
 project: default
 source:
  repoURL: 'https://github.com/rancher/local-path-provisioner.git'
  path: deploy/chart/local-path-provisioner
  targetRevision: HEAD
  helm:
    parameters:
        - name: rbac.create
          value: "true"
        - name: storageClass.defaultClass
          value: "true"
 destination:
  server: 'https://kubernetes.default.svc'
  namespace: local-path-storage
 syncPolicy:
  automated: {}
  syncOptions:
    - CreateNamespace=true
{{- end }}
