apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: jackett
  namespace: argocd
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: 'https://rubxkube.github.io/charts'
    chart: jackett
    targetRevision: 1.3.0
    helm:
      parameters:
        - name: common.image.tag
          value: latest
        # - name: common.service.type
        #   value: LoadBalancer
        - name: common.persistence.volumes[0].name
          value: config
        - name: common.persistence.volumes[0].storageClassName
          value: local-path
        - name: common.persistence.volumes[0].size
          value: 2Gi
        - name: common.persistence.volumes[0].containerMount
          value: /config
        - name: common.persistence.volumes[1].name
          value: downloads
        - name: common.persistence.volumes[1].storageClassName
          value: local-path
        - name: common.persistence.volumes[1].size
          value: 5Gi
        - name: common.persistence.volumes[1].containerMount
          value: /downloads
        - name: common.variables.nonSecret.TZ
          value: Asia/<PERSON>_<PERSON>_<PERSON>
        - name: common.variables.nonSecret.AUTO_UPDATE
          value: "true"
        - name: common.variables.nonSecret.JACKETT_AUTOMATIC_SEARCH_TIMEOUT
          value: "120000"
        - name: common.variables.nonSecret.JACKETT_MANUAL_SEARCH_TIMEOUT
          value: "15000"
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: media
  syncPolicy:
    automated: {}
    syncOptions:
      - CreateNamespace=true
