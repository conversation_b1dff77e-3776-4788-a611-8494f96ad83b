{{- range $name, $app := .Values.apps }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ $name }}
  namespace: argocd
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
 project: default
 source:
  repoURL: '**************:dungxtd/helm-template.git'
  path: .
  targetRevision: HEAD
  helm:
    valueFiles:
      - values.yaml
    parameters:
      - name: nameOverride
        value: {{ $name }}
      - name: image.repository
        value: {{ $app.image.repository }}
      - name: image.tag
        value: "{{ $app.image.tag }}"
      - name: container.port.number
        value: "{{ printf "%v" $app.containerPort }}"
      - name: storage
        value: "{{ $app.storage }}"
      - name: storageClassName
        value: {{ default "local-path" $app.storageClassName }}
    values: |
      imagePullSecrets:
        - name: image-pull-secret
 destination:
  server: 'https://kubernetes.default.svc'
  namespace: {{ $name }}
 syncPolicy:
  automated: {}
  syncOptions:
    - CreateNamespace=true
---
{{- end }}
