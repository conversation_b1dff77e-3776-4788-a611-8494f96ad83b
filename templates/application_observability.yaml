apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: observability
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: 'https://prometheus-community.github.io/helm-charts'
    targetRevision: 75.6.0
    helm:
      skipCrds: true
      values: |
      {{ if .Values.observability.pdkey }}
        alertmanager:
          config:
            route:
              group_by: ['namespace']
              group_wait: 30s
              group_interval: 5m
              repeat_interval: 12h
              receiver: default
              routes:
              - receiver: default
                matchers:
                  - alertname =~ "InfoInhibitor|Watchdog"
            receivers:
              - name: default
                pagerduty_configs:
                - service_key: {{ .Values.observability.pdkey }}
      {{ end }}
        prometheus:
          prometheusSpec:
            podMonitorNamespaceSelector:
            any: true
            podMonitorSelector: {}
            podMonitorSelectorNilUsesHelmValues: false
            ruleNamespaceSelector:
              any: true
            ruleSelector: {}
            ruleSelectorNilUsesHelmValues: false
            serviceMonitorNamespaceSelector:
              any: true
            serviceMonitorSelector: {}
            serviceMonitorSelectorNilUsesHelmValues: false
            retention: {{ .Values.observability.retention }}
            storageSpec:
              volumeClaimTemplate:
                spec:
                  storageClassName: {{ .Values.observability.storageClass }}
                  accessModes: ["ReadWriteOnce"]
                  resources:
                    requests:
                      storage: {{ .Values.observability.storageSize }}
        grafana:
          sidecar:
            dashboards:
              searchNamespace: ALL
          service:
            type: ClusterIP
          additionalDataSources:
            - name: loki
              type: loki
              url: http://loki.observability.svc.cluster.local:3100
            - name: tempo
              type: tempo
              url: http://tempo.observability.svc.cluster.local:3100

    chart: kube-prometheus-stack
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: observability
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
