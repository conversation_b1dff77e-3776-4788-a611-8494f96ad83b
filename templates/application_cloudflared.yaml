apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cloudflared
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  destination:
    namespace: cloudflared
    server: https://kubernetes.default.svc
  project: default
  source:
    chart: cloudflare-tunnel
    helm:
      parameters:
        - name: cloudflare.account
          value: "{{ .Values.cloudflare.account }}"
        - name: cloudflare.tunnelName
          value: "{{ .Values.cloudflare.tunnelName }}"
        - name: cloudflare.tunnelId
          value: "{{ .Values.cloudflare.tunnelId }}"
        - name: cloudflare.secret
          value: "{{ .Values.cloudflare.secret }}"
        - name: image.tag
          value: "2025.2.1"
        - name: replicaCount
          value: "5"
      values: |
        cloudflare:
          ingress:
          {{- range .Values.cloudflare.services }}
          {{- if .prefix }}
          - hostname: "{{ .prefix }}.{{ $.Values.domain }}"
            service: "{{ .protocol }}://{{ .name }}.{{ .namespace }}:{{ .port }}"
            {{- if .originRequest }}
            originRequest: {{ toYaml .originRequest | nindent 14 }}
            {{- end }}
          {{- end }}
          {{- if .isDefault }}
          - hostname: "{{ $.Values.domain }}"
            service: "{{ .protocol }}://{{ .name }}.{{ .namespace }}:{{ .port }}"
            {{- if .originRequest }}
            originRequest: {{ toYaml .originRequest | nindent 14 }}
            {{- end }}
          - hostname: "*.{{ $.Values.domain }}"
            service: "{{ .protocol }}://{{ .name }}.{{ .namespace }}:{{ .port }}"
            {{- if .originRequest }}
            originRequest: {{ toYaml .originRequest | nindent 14 }}
            {{- end }}
          {{- end }}
          {{- end }}
    repoURL: https://cloudflare.github.io/helm-charts
    targetRevision: 0.3.2
  syncPolicy:
    automated:
      prune: true
    syncOptions:
      - CreateNamespace=true
