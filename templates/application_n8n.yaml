apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: n8n
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  destination:
    namespace: n8n
    server: https://kubernetes.default.svc
  project: default
  source:
    chart: n8n
    helm:
      values: |
        _shared_config:
          hostname: &hostname n8n.{{ .Values.domain }}
          url: &url https://n8n.{{ .Values.domain }}/
          webhook_tunnel_url: &webhook_tunnel_url https://n8n.{{ .Values.domain }}/webhook/
        config:
          database:
            type: postgresdb
            postgresdb:
              database: n8n-postgres
              host: n8n-postgres-postgresql.n8n.svc.cluster.local
        secret:
          database:
            postgresdb:
              user: postgres
              password: postgres
        extraEnv:
          N8N_HOST: *hostname
          N8N_PROTOCOL: https
          WEBHOOK_URL: *url
          GENERIC_TIMEZONE: "Asia/Ho_Chi_Minh"
          N8N_ENCRYPTION_KEY: hehehe
          NODE_ENV: production
          N8N_LICENSE_ACTIVATION_KEY: ce3b6bc8-acb6-4f2d-a6a5-aa2b9b789c88
          N8N_EXPRESS_TRUST_PROXY: true
        image:
          repository: dungxtd/n8n-pupeteer
          tag: "1.84.1"
        scaling:
          enabled: true
          redis:
            host: n8n-redis-master.n8n.svc.cluster.local
            password: "redis"
    repoURL: 8gears.container-registry.com/library
    targetRevision: 0.25.0
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true
