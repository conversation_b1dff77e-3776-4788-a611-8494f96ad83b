apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cert-manager
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: 'https://charts.jetstack.io'
    targetRevision: 1.18.1
    helm:
      parameters:
        - name: installCRDs
          value: 'true'
        - name: prometheus.servicemonitor.enabled
          value: 'false'
        - name: prometheus.servicemonitor.honorLabels
          value: 'true'
    chart: cert-manager
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: cert-manager
  syncPolicy:
    automated: {}
    syncOptions:
      - CreateNamespace=true
