apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: n8n-postgres
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  destination:
    namespace: n8n
    server: https://kubernetes.default.svc
  project: default
  source:
    chart: postgresql
    helm:
      values: |
        global:
            defaultStorageClass: "local-path"
            storageClass: "local-path"
        auth:
          postgresPassword: postgres
          username: postgres
          password: postgres
          database: n8n-postgres
        primary:
          persistence:
            enabled: true
            size: 8Gi
    repoURL: registry-1.docker.io/bitnamicharts
    targetRevision: 16.5.5
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true