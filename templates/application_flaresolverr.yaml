apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: flaresolverr
  namespace: argocd
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: 'https://rubxkube.github.io/charts'
    chart: flaresolverr
    targetRevision: 0.0.1
    helm:
      parameters:
        - name: common.image.tag
          value: latest
        - name: common.variables.nonSecret.TZ
          value: Asia/Ho_Chi_Minh
        - name: common.variables.nonSecret.FS_BROWSER_TIMEOUT
          value: "30000"
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: media
  syncPolicy:
    automated: {}
    syncOptions:
      - CreateNamespace=true