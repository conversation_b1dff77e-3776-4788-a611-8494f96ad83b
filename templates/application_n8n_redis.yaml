apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: n8n-redis
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  destination:
    namespace: n8n
    server: https://kubernetes.default.svc
  project: default
  source:
    chart: redis
    helm:
      values: |
        global:
          storageClass: "local-path"
          defaultStorageClass: "local-path"
        auth:
          password: "redis"
        architecture: standalone
        master:
          persistence:
            enabled: true
            size: 8Gi
    repoURL: registry-1.docker.io/bitnamicharts
    targetRevision: 20.11.3
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true