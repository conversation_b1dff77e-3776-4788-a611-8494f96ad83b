{{- if .Values.logging.enable }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: logging
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "2"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: 'https://grafana.github.io/helm-charts'
    targetRevision: 2.10.2
    chart: loki-stack
    helm:
      releaseName: loki
      parameters:
      - name: grafana.sidecar.datasources.enabled
        value: "true"
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: observability
  syncPolicy:
    automated:
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
{{- end }}
