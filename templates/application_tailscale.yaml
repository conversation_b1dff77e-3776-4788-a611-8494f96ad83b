apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tailscale
  namespace: argocd
spec:
  destination:
    namespace: tailscale
    server: https://kubernetes.default.svc
  project: default
  source:
    chart: tailscale-operator
    helm:
      values: |
        oauth:
          clientId: {{.Values.tailscale.clientId }}
          clientSecret: {{.Values.tailscale.clientSecret }}
        apiServerProxyConfig:
          mode: "true"
    repoURL: https://pkgs.tailscale.com/helmcharts
    targetRevision: 1.84.3
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true
